/**
 * NuxtOptionsLoading
 * Documentation: https://nuxtjs.org/api/configuration-loading
 */

export interface NuxtOptionsLoading {
  color?: string
  continuous?: boolean
  css?: boolean
  duration?: number
  failedColor?: string
  height?: string
  rtl?: boolean
  throttle?: number
}

/**
 * NuxtOptionsLoadingIndicator
 * Documentation: https://nuxtjs.org/api/configuration-loading-indicator
 */

export interface NuxtOptionsLoadingIndicator {
  background?: string
  color?: string
  color2?: string
  name?: string
}
