{"name": "@types/etag", "version": "1.8.3", "description": "TypeScript definitions for etag", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/etag", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/etag"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "621f98467ca98f31fee221ba7b6553772408c6aa7f637563e6a68d1f0891da46", "typeScriptVersion": "4.5"}