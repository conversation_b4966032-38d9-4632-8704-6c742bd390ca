{"name": "@types/connect", "version": "3.4.38", "description": "TypeScript definitions for connect", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "githubUsername": "SomaticIT", "url": "https://github.com/SomaticIT"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/EvanHahn"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/connect"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "8990242237504bdec53088b79e314b94bec69286df9de56db31f22de403b4092", "typeScriptVersion": "4.5"}