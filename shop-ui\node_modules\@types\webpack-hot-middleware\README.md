# Installation
> `npm install --save @types/webpack-hot-middleware`

# Summary
This package contains type definitions for webpack-hot-middleware (https://github.com/webpack-contrib/webpack-hot-middleware).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-hot-middleware.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-hot-middleware/index.d.ts)
````ts
// Type definitions for webpack-hot-middleware 2.25
// Project: https://github.com/webpack-contrib/webpack-hot-middleware
// Definitions by: <PERSON> <https://github.com/bumbleblym>
//               <PERSON> <https://github.com/icylace>
//               <PERSON> <https://github.com/chrisabrams>
//               <PERSON><PERSON> <https://github.com/il<PERSON><PERSON><PERSON><PERSON>>
//               <PERSON> <https://github.com/saboya>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// TypeScript Version: 3.7

import { NextHandleFunction } from 'connect';
import * as webpack from 'webpack';

export = WebpackHotMiddleware;

declare function WebpackHotMiddleware(
    compiler: webpack.ICompiler,
    options?: WebpackHotMiddleware.MiddlewareOptions
): NextHandleFunction & WebpackHotMiddleware.EventStream;

declare namespace WebpackHotMiddleware {
    interface ClientOptions {
        path?: string | undefined;
        reload?: boolean | undefined;
        name?: string | undefined;
        timeout?: number | undefined;
        overlay?: boolean | undefined;
        noInfo?: boolean | undefined;
        quiet?: boolean | undefined;
        dynamicPublicPath?: boolean | undefined;
        autoConnect?: boolean | undefined;
        ansiColors?: {
            [key: string]: any
        } | undefined;
        overlayStyles?: {
            [key: string]: any
        } | undefined;
        overlayWarnings?: boolean | undefined;
    }
    interface MiddlewareOptions {
        log?: false | Logger | undefined;
        path?: string | undefined;
        heartbeat?: number | undefined;
    }

    type Logger = (message?: any, ...optionalParams: any[]) => void;

    interface EventStream {
        publish(payload: any): void;
        close(): void;
    }
}

````

### Additional Details
 * Last updated: Fri, 02 Jul 2021 18:05:28 GMT
 * Dependencies: [@types/webpack](https://npmjs.com/package/@types/webpack), [@types/connect](https://npmjs.com/package/@types/connect)
 * Global values: none

# Credits
These definitions were written by [Benjamin Lim](https://github.com/bumbleblym), [Ron Martinez](https://github.com/icylace), [Chris Abrams](https://github.com/chrisabrams), [Ilya Zelenko](https://github.com/iliyaZelenko), and [Rodrigo Saboya](https://github.com/saboya).
