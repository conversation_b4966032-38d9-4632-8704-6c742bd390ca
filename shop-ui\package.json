{"name": "nuxt", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate"}, "dependencies": {"@nuxtjs/axios": "^5.13.6", "@nuxtjs/proxy": "^2.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.27.2", "clipboard": "^2.0.11", "core-js": "^3.19.3", "crypto-js": "^4.2.0", "echarts": "^5.3.2", "element-ui": "^2.15.6", "file-saver": "^2.0.2", "js-cookie": "^3.0.1", "mathjs": "^10.6.1", "nuxt": "^2.15.8", "qrcode": "^1.5.3", "qs": "^6.11.0", "vue": "^2.6.14", "vue-jsonp": "^2.0.0", "vue-quill-editor": "^3.0.6", "vue-seamless-scroll": "^1.1.23", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "webpack": "^4.46.0", "xlsx": "^0.16.0"}, "devDependencies": {"@nuxt/types": "~2.17.0", "sass": "1.32.13", "sass-loader": "10.1.1"}, "config": {"nuxt": {"host": "0.0.0.0", "port": "3000"}}}