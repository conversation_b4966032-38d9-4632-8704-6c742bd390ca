import Vue from 'vue'
import Router from 'vue-router'
import { normalizeURL, decode } from 'ufo'
import { interopDefault } from './utils'
import scrollBehavior from './router.scrollBehavior.js'

const _67c10cd4 = () => interopDefault(import('..\\pages\\cart\\index.vue' /* webpackChunkName: "pages/cart/index" */))
const _c7e02632 = () => interopDefault(import('..\\pages\\clause\\index.vue' /* webpackChunkName: "pages/clause/index" */))
const _a721be76 = () => interopDefault(import('..\\pages\\detail\\index.vue' /* webpackChunkName: "pages/detail/index" */))
const _a3eafbde = () => interopDefault(import('..\\pages\\feedback\\index.vue' /* webpackChunkName: "pages/feedback/index" */))
const _6c09929a = () => interopDefault(import('..\\pages\\infor\\index.vue' /* webpackChunkName: "pages/infor/index" */))
const _d2c0b8d6 = () => interopDefault(import('..\\pages\\login\\index.vue' /* webpackChunkName: "pages/login/index" */))
const _3371194f = () => interopDefault(import('..\\pages\\product\\index.vue' /* webpackChunkName: "pages/product/index" */))
const _651b370f = () => interopDefault(import('..\\pages\\sso-callback.vue' /* webpackChunkName: "pages/sso-callback" */))
const _a19f01c6 = () => interopDefault(import('..\\pages\\store\\index.vue' /* webpackChunkName: "pages/store/index" */))
const _362b8bdc = () => interopDefault(import('..\\pages\\cart\\order.vue' /* webpackChunkName: "pages/cart/order" */))
const _52594979 = () => interopDefault(import('..\\pages\\error\\401.vue' /* webpackChunkName: "pages/error/401" */))
const _52838ffc = () => interopDefault(import('..\\pages\\error\\404.vue' /* webpackChunkName: "pages/error/404" */))
const _7462fcb1 = () => interopDefault(import('..\\pages\\product\\list.vue' /* webpackChunkName: "pages/product/list" */))
const _0f993b09 = () => interopDefault(import('..\\pages\\product\\shop.vue' /* webpackChunkName: "pages/product/shop" */))
const _1faccd88 = () => interopDefault(import('..\\pages\\purchase\\afterSale\\index.vue' /* webpackChunkName: "pages/purchase/afterSale/index" */))
const _2d961d83 = () => interopDefault(import('..\\pages\\purchase\\contract\\index.vue' /* webpackChunkName: "pages/purchase/contract/index" */))
const _b461373e = () => interopDefault(import('..\\pages\\purchase\\duizhang\\index.vue' /* webpackChunkName: "pages/purchase/duizhang/index" */))
const _3a6bbfcd = () => interopDefault(import('..\\pages\\purchase\\index\\index.vue' /* webpackChunkName: "pages/purchase/index/index" */))
const _e35d8b10 = () => interopDefault(import('..\\pages\\purchase\\inquiry\\index.vue' /* webpackChunkName: "pages/purchase/inquiry/index" */))
const _51c936d8 = () => interopDefault(import('..\\pages\\purchase\\supplierGrade\\index.vue' /* webpackChunkName: "pages/purchase/supplierGrade/index" */))
const _72af9b60 = () => interopDefault(import('..\\pages\\purchase\\supplierOverview\\index.vue' /* webpackChunkName: "pages/purchase/supplierOverview/index" */))
const _db2b92a8 = () => interopDefault(import('..\\pages\\purchase\\wuliao\\index.vue' /* webpackChunkName: "pages/purchase/wuliao/index" */))
const _5758a523 = () => interopDefault(import('..\\pages\\store\\list.vue' /* webpackChunkName: "pages/store/list" */))
const _3ac38426 = () => interopDefault(import('..\\pages\\store\\search.vue' /* webpackChunkName: "pages/store/search" */))
const _4e7713aa = () => interopDefault(import('..\\pages\\supply\\afterSale\\index.vue' /* webpackChunkName: "pages/supply/afterSale/index" */))
const _0f989d55 = () => interopDefault(import('..\\pages\\supply\\contract\\index.vue' /* webpackChunkName: "pages/supply/contract/index" */))
const _d8f8bd8a = () => interopDefault(import('..\\pages\\supply\\index\\index.vue' /* webpackChunkName: "pages/supply/index/index" */))
const _3a6936cd = () => interopDefault(import('..\\pages\\supply\\purchaseOverview\\index.vue' /* webpackChunkName: "pages/supply/purchaseOverview/index" */))
const _09c80a7c = () => interopDefault(import('..\\pages\\cart\\components\\site-dialog.vue' /* webpackChunkName: "pages/cart/components/site-dialog" */))
const _ec18bac2 = () => interopDefault(import('..\\pages\\purchase\\center\\enterprise\\index.vue' /* webpackChunkName: "pages/purchase/center/enterprise/index" */))
const _7e42528d = () => interopDefault(import('..\\pages\\purchase\\center\\message\\index.vue' /* webpackChunkName: "pages/purchase/center/message/index" */))
const _f4ac60aa = () => interopDefault(import('..\\pages\\purchase\\center\\profile\\index.vue' /* webpackChunkName: "pages/purchase/center/profile/index" */))
const _03e882bc = () => interopDefault(import('..\\pages\\purchase\\order\\add\\index.vue' /* webpackChunkName: "pages/purchase/order/add/index" */))
const _2bde17b3 = () => interopDefault(import('..\\pages\\purchase\\order\\list\\index.vue' /* webpackChunkName: "pages/purchase/order/list/index" */))
const _10140c3b = () => interopDefault(import('..\\pages\\purchase\\order\\pay\\index.vue' /* webpackChunkName: "pages/purchase/order/pay/index" */))
const _7d3e4223 = () => interopDefault(import('..\\pages\\purchase\\order\\warehousing\\index.vue' /* webpackChunkName: "pages/purchase/order/warehousing/index" */))
const _0f24232c = () => interopDefault(import('..\\pages\\purchase\\system\\org\\index.vue' /* webpackChunkName: "pages/purchase/system/org/index" */))
const _29afed96 = () => interopDefault(import('..\\pages\\purchase\\system\\perm\\index.vue' /* webpackChunkName: "pages/purchase/system/perm/index" */))
const _491d5f60 = () => interopDefault(import('..\\pages\\purchase\\system\\role\\index.vue' /* webpackChunkName: "pages/purchase/system/role/index" */))
const _2c83f81e = () => interopDefault(import('..\\pages\\supply\\center\\enterprise\\index.vue' /* webpackChunkName: "pages/supply/center/enterprise/index" */))
const _bc0c7b0a = () => interopDefault(import('..\\pages\\supply\\center\\message\\index.vue' /* webpackChunkName: "pages/supply/center/message/index" */))
const _ad3d80ce = () => interopDefault(import('..\\pages\\supply\\center\\profile\\index.vue' /* webpackChunkName: "pages/supply/center/profile/index" */))
const _2cb6aea1 = () => interopDefault(import('..\\pages\\supply\\order\\list\\index.vue' /* webpackChunkName: "pages/supply/order/list/index" */))
const _3ee4cae2 = () => interopDefault(import('..\\pages\\supply\\order\\outbound\\index.vue' /* webpackChunkName: "pages/supply/order/outbound/index" */))
const _7b75df8d = () => interopDefault(import('..\\pages\\supply\\order\\pay\\index.vue' /* webpackChunkName: "pages/supply/order/pay/index" */))
const _40ea16ce = () => interopDefault(import('..\\pages\\supply\\project\\inquiry\\index.vue' /* webpackChunkName: "pages/supply/project/inquiry/index" */))
const _ecfdbb38 = () => interopDefault(import('..\\pages\\supply\\project\\offer\\index.vue' /* webpackChunkName: "pages/supply/project/offer/index" */))
const _7265b3b0 = () => interopDefault(import('..\\pages\\supply\\shop\\library\\index.vue' /* webpackChunkName: "pages/supply/shop/library/index" */))
const _82b7d518 = () => interopDefault(import('..\\pages\\supply\\shop\\product\\index.vue' /* webpackChunkName: "pages/supply/shop/product/index" */))
const _69afff0c = () => interopDefault(import('..\\pages\\supply\\shop\\profile\\index.vue' /* webpackChunkName: "pages/supply/shop/profile/index" */))
const _26b3071e = () => interopDefault(import('..\\pages\\supply\\shop\\renovation\\index.vue' /* webpackChunkName: "pages/supply/shop/renovation/index" */))
const _0d72f550 = () => interopDefault(import('..\\pages\\supply\\system\\org\\index.vue' /* webpackChunkName: "pages/supply/system/org/index" */))
const _43ea3468 = () => interopDefault(import('..\\pages\\supply\\system\\perm\\index.vue' /* webpackChunkName: "pages/supply/system/perm/index" */))
const _14a8d1bc = () => interopDefault(import('..\\pages\\supply\\system\\role\\index.vue' /* webpackChunkName: "pages/supply/system/role/index" */))
const _19a28df9 = () => interopDefault(import('..\\pages\\purchase\\inquiry\\components\\importTable.vue' /* webpackChunkName: "pages/purchase/inquiry/components/importTable" */))
const _26619d8e = () => interopDefault(import('..\\pages\\purchase\\inquiry\\components\\lessDetails.vue' /* webpackChunkName: "pages/purchase/inquiry/components/lessDetails" */))
const _5cc8827e = () => interopDefault(import('..\\pages\\purchase\\inquiry\\components\\list.vue' /* webpackChunkName: "pages/purchase/inquiry/components/list" */))
const _81cb2c06 = () => interopDefault(import('..\\pages\\purchase\\inquiry\\components\\moreDetails.vue' /* webpackChunkName: "pages/purchase/inquiry/components/moreDetails" */))
const _48a520ac = () => interopDefault(import('..\\pages\\purchase\\inquiry\\components\\supplier.vue' /* webpackChunkName: "pages/purchase/inquiry/components/supplier" */))
const _475350cb = () => interopDefault(import('..\\pages\\supply\\shop\\product\\importTable.vue' /* webpackChunkName: "pages/supply/shop/product/importTable" */))
const _c7679cbc = () => interopDefault(import('..\\pages\\supply\\shop\\product\\productdetail.vue' /* webpackChunkName: "pages/supply/shop/product/productdetail" */))
const _5a74db4e = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\afterSale.vue' /* webpackChunkName: "pages/purchase/order/list/components/afterSale" */))
const _26fad892 = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\applyList.vue' /* webpackChunkName: "pages/purchase/order/list/components/applyList" */))
const _78f29209 = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\collectOrder.vue' /* webpackChunkName: "pages/purchase/order/list/components/collectOrder" */))
const _3170a263 = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\list.vue' /* webpackChunkName: "pages/purchase/order/list/components/list" */))
const _06895942 = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\list2.vue' /* webpackChunkName: "pages/purchase/order/list/components/list2" */))
const _c470e566 = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\myOrder.vue' /* webpackChunkName: "pages/purchase/order/list/components/myOrder" */))
const _2d4d18d9 = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\orderDetails.vue' /* webpackChunkName: "pages/purchase/order/list/components/orderDetails" */))
const _1c7d90bd = () => interopDefault(import('..\\pages\\purchase\\order\\list\\components\\orderForm.vue' /* webpackChunkName: "pages/purchase/order/list/components/orderForm" */))
const _314dd63c = () => interopDefault(import('..\\pages\\supply\\order\\list\\components\\afterSale.vue' /* webpackChunkName: "pages/supply/order/list/components/afterSale" */))
const _f5a8ef4a = () => interopDefault(import('..\\pages\\supply\\order\\list\\components\\collectOrder.vue' /* webpackChunkName: "pages/supply/order/list/components/collectOrder" */))
const _10b3ea96 = () => interopDefault(import('..\\pages\\supply\\order\\list\\components\\list.vue' /* webpackChunkName: "pages/supply/order/list/components/list" */))
const _059a1566 = () => interopDefault(import('..\\pages\\supply\\order\\list\\components\\list2.vue' /* webpackChunkName: "pages/supply/order/list/components/list2" */))
const _5ede6bbb = () => interopDefault(import('..\\pages\\supply\\order\\list\\components\\myOrder.vue' /* webpackChunkName: "pages/supply/order/list/components/myOrder" */))
const _7d9bb0f8 = () => interopDefault(import('..\\pages\\supply\\order\\list\\components\\orderDetail.vue' /* webpackChunkName: "pages/supply/order/list/components/orderDetail" */))
const _0fc5051a = () => interopDefault(import('..\\pages\\supply\\project\\inquiry\\components\\lessDetails.vue' /* webpackChunkName: "pages/supply/project/inquiry/components/lessDetails" */))
const _3bdf8444 = () => interopDefault(import('..\\pages\\supply\\project\\inquiry\\components\\moreDetails.vue' /* webpackChunkName: "pages/supply/project/inquiry/components/moreDetails" */))
const _71857625 = () => interopDefault(import('..\\pages\\supply\\project\\offer\\components\\lessDetails.vue' /* webpackChunkName: "pages/supply/project/offer/components/lessDetails" */))
const _43d0aee9 = () => interopDefault(import('..\\pages\\supply\\project\\offer\\components\\moreDetails.vue' /* webpackChunkName: "pages/supply/project/offer/components/moreDetails" */))
const _b560e594 = () => interopDefault(import('..\\pages\\index.vue' /* webpackChunkName: "pages/index" */))

const emptyFn = () => {}

Vue.use(Router)

export const routerOptions = {
  mode: 'history',
  base: '/chain/',
  linkActiveClass: 'nuxt-link-active',
  linkExactActiveClass: 'nuxt-link-exact-active',
  scrollBehavior,

  routes: [{
    path: "/cart",
    component: _67c10cd4,
    name: "cart"
  }, {
    path: "/clause",
    component: _c7e02632,
    name: "clause"
  }, {
    path: "/detail",
    component: _a721be76,
    name: "detail"
  }, {
    path: "/feedback",
    component: _a3eafbde,
    name: "feedback"
  }, {
    path: "/infor",
    component: _6c09929a,
    name: "infor"
  }, {
    path: "/login",
    component: _d2c0b8d6,
    name: "login"
  }, {
    path: "/product",
    component: _3371194f,
    name: "product"
  }, {
    path: "/sso-callback",
    component: _651b370f,
    name: "sso-callback"
  }, {
    path: "/store",
    component: _a19f01c6,
    name: "store"
  }, {
    path: "/cart/order",
    component: _362b8bdc,
    name: "cart-order"
  }, {
    path: "/error/401",
    component: _52594979,
    name: "error-401"
  }, {
    path: "/error/404",
    component: _52838ffc,
    name: "error-404"
  }, {
    path: "/product/list",
    component: _7462fcb1,
    name: "product-list"
  }, {
    path: "/product/shop",
    component: _0f993b09,
    name: "product-shop"
  }, {
    path: "/purchase/afterSale",
    component: _1faccd88,
    name: "purchase-afterSale"
  }, {
    path: "/purchase/contract",
    component: _2d961d83,
    name: "purchase-contract"
  }, {
    path: "/purchase/duizhang",
    component: _b461373e,
    name: "purchase-duizhang"
  }, {
    path: "/purchase/index",
    component: _3a6bbfcd,
    name: "purchase-index"
  }, {
    path: "/purchase/inquiry",
    component: _e35d8b10,
    name: "purchase-inquiry"
  }, {
    path: "/purchase/supplierGrade",
    component: _51c936d8,
    name: "purchase-supplierGrade"
  }, {
    path: "/purchase/supplierOverview",
    component: _72af9b60,
    name: "purchase-supplierOverview"
  }, {
    path: "/purchase/wuliao",
    component: _db2b92a8,
    name: "purchase-wuliao"
  }, {
    path: "/store/list",
    component: _5758a523,
    name: "store-list"
  }, {
    path: "/store/search",
    component: _3ac38426,
    name: "store-search"
  }, {
    path: "/supply/afterSale",
    component: _4e7713aa,
    name: "supply-afterSale"
  }, {
    path: "/supply/contract",
    component: _0f989d55,
    name: "supply-contract"
  }, {
    path: "/supply/index",
    component: _d8f8bd8a,
    name: "supply-index"
  }, {
    path: "/supply/purchaseOverview",
    component: _3a6936cd,
    name: "supply-purchaseOverview"
  }, {
    path: "/cart/components/site-dialog",
    component: _09c80a7c,
    name: "cart-components-site-dialog"
  }, {
    path: "/purchase/center/enterprise",
    component: _ec18bac2,
    name: "purchase-center-enterprise"
  }, {
    path: "/purchase/center/message",
    component: _7e42528d,
    name: "purchase-center-message"
  }, {
    path: "/purchase/center/profile",
    component: _f4ac60aa,
    name: "purchase-center-profile"
  }, {
    path: "/purchase/order/add",
    component: _03e882bc,
    name: "purchase-order-add"
  }, {
    path: "/purchase/order/list",
    component: _2bde17b3,
    name: "purchase-order-list"
  }, {
    path: "/purchase/order/pay",
    component: _10140c3b,
    name: "purchase-order-pay"
  }, {
    path: "/purchase/order/warehousing",
    component: _7d3e4223,
    name: "purchase-order-warehousing"
  }, {
    path: "/purchase/system/org",
    component: _0f24232c,
    name: "purchase-system-org"
  }, {
    path: "/purchase/system/perm",
    component: _29afed96,
    name: "purchase-system-perm"
  }, {
    path: "/purchase/system/role",
    component: _491d5f60,
    name: "purchase-system-role"
  }, {
    path: "/supply/center/enterprise",
    component: _2c83f81e,
    name: "supply-center-enterprise"
  }, {
    path: "/supply/center/message",
    component: _bc0c7b0a,
    name: "supply-center-message"
  }, {
    path: "/supply/center/profile",
    component: _ad3d80ce,
    name: "supply-center-profile"
  }, {
    path: "/supply/order/list",
    component: _2cb6aea1,
    name: "supply-order-list"
  }, {
    path: "/supply/order/outbound",
    component: _3ee4cae2,
    name: "supply-order-outbound"
  }, {
    path: "/supply/order/pay",
    component: _7b75df8d,
    name: "supply-order-pay"
  }, {
    path: "/supply/project/inquiry",
    component: _40ea16ce,
    name: "supply-project-inquiry"
  }, {
    path: "/supply/project/offer",
    component: _ecfdbb38,
    name: "supply-project-offer"
  }, {
    path: "/supply/shop/library",
    component: _7265b3b0,
    name: "supply-shop-library"
  }, {
    path: "/supply/shop/product",
    component: _82b7d518,
    name: "supply-shop-product"
  }, {
    path: "/supply/shop/profile",
    component: _69afff0c,
    name: "supply-shop-profile"
  }, {
    path: "/supply/shop/renovation",
    component: _26b3071e,
    name: "supply-shop-renovation"
  }, {
    path: "/supply/system/org",
    component: _0d72f550,
    name: "supply-system-org"
  }, {
    path: "/supply/system/perm",
    component: _43ea3468,
    name: "supply-system-perm"
  }, {
    path: "/supply/system/role",
    component: _14a8d1bc,
    name: "supply-system-role"
  }, {
    path: "/purchase/inquiry/components/importTable",
    component: _19a28df9,
    name: "purchase-inquiry-components-importTable"
  }, {
    path: "/purchase/inquiry/components/lessDetails",
    component: _26619d8e,
    name: "purchase-inquiry-components-lessDetails"
  }, {
    path: "/purchase/inquiry/components/list",
    component: _5cc8827e,
    name: "purchase-inquiry-components-list"
  }, {
    path: "/purchase/inquiry/components/moreDetails",
    component: _81cb2c06,
    name: "purchase-inquiry-components-moreDetails"
  }, {
    path: "/purchase/inquiry/components/supplier",
    component: _48a520ac,
    name: "purchase-inquiry-components-supplier"
  }, {
    path: "/supply/shop/product/importTable",
    component: _475350cb,
    name: "supply-shop-product-importTable"
  }, {
    path: "/supply/shop/product/productdetail",
    component: _c7679cbc,
    name: "supply-shop-product-productdetail"
  }, {
    path: "/purchase/order/list/components/afterSale",
    component: _5a74db4e,
    name: "purchase-order-list-components-afterSale"
  }, {
    path: "/purchase/order/list/components/applyList",
    component: _26fad892,
    name: "purchase-order-list-components-applyList"
  }, {
    path: "/purchase/order/list/components/collectOrder",
    component: _78f29209,
    name: "purchase-order-list-components-collectOrder"
  }, {
    path: "/purchase/order/list/components/list",
    component: _3170a263,
    name: "purchase-order-list-components-list"
  }, {
    path: "/purchase/order/list/components/list2",
    component: _06895942,
    name: "purchase-order-list-components-list2"
  }, {
    path: "/purchase/order/list/components/myOrder",
    component: _c470e566,
    name: "purchase-order-list-components-myOrder"
  }, {
    path: "/purchase/order/list/components/orderDetails",
    component: _2d4d18d9,
    name: "purchase-order-list-components-orderDetails"
  }, {
    path: "/purchase/order/list/components/orderForm",
    component: _1c7d90bd,
    name: "purchase-order-list-components-orderForm"
  }, {
    path: "/supply/order/list/components/afterSale",
    component: _314dd63c,
    name: "supply-order-list-components-afterSale"
  }, {
    path: "/supply/order/list/components/collectOrder",
    component: _f5a8ef4a,
    name: "supply-order-list-components-collectOrder"
  }, {
    path: "/supply/order/list/components/list",
    component: _10b3ea96,
    name: "supply-order-list-components-list"
  }, {
    path: "/supply/order/list/components/list2",
    component: _059a1566,
    name: "supply-order-list-components-list2"
  }, {
    path: "/supply/order/list/components/myOrder",
    component: _5ede6bbb,
    name: "supply-order-list-components-myOrder"
  }, {
    path: "/supply/order/list/components/orderDetail",
    component: _7d9bb0f8,
    name: "supply-order-list-components-orderDetail"
  }, {
    path: "/supply/project/inquiry/components/lessDetails",
    component: _0fc5051a,
    name: "supply-project-inquiry-components-lessDetails"
  }, {
    path: "/supply/project/inquiry/components/moreDetails",
    component: _3bdf8444,
    name: "supply-project-inquiry-components-moreDetails"
  }, {
    path: "/supply/project/offer/components/lessDetails",
    component: _71857625,
    name: "supply-project-offer-components-lessDetails"
  }, {
    path: "/supply/project/offer/components/moreDetails",
    component: _43d0aee9,
    name: "supply-project-offer-components-moreDetails"
  }, {
    path: "/",
    component: _b560e594,
    name: "index"
  }],

  fallback: false
}

export function createRouter (ssrContext, config) {
  const base = (config._app && config._app.basePath) || routerOptions.base
  const router = new Router({ ...routerOptions, base  })

  // TODO: remove in Nuxt 3
  const originalPush = router.push
  router.push = function push (location, onComplete = emptyFn, onAbort) {
    return originalPush.call(this, location, onComplete, onAbort)
  }

  const resolve = router.resolve.bind(router)
  router.resolve = (to, current, append) => {
    if (typeof to === 'string') {
      to = normalizeURL(to)
    }
    return resolve(to, current, append)
  }

  return router
}
