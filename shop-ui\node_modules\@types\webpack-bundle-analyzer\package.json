{"name": "@types/webpack-bundle-analyzer", "version": "3.9.5", "description": "TypeScript definitions for webpack-bundle-analyzer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-bundle-analyzer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/kryops", "githubUsername": "kryops"}, {"name": "<PERSON>", "url": "https://github.com/maxbogus", "githubUsername": "maxbogus"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/k-yle", "githubUsername": "k-yle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-bundle-analyzer"}, "scripts": {}, "dependencies": {"@types/webpack": "^4"}, "typesPublisherContentHash": "9edf374aa55731102c1375def636761edc6afddeb4dc50a11a45211147bd973b", "typeScriptVersion": "4.1"}