{"name": "@types/less", "version": "3.0.6", "description": "TypeScript definitions for less", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/less", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "thasner", "url": "https://github.com/thasner"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "pranaygp", "url": "https://github.com/pranaygp"}, {"name": "<PERSON>", "githubUsername": "dwaxweiler", "url": "https://github.com/dwaxweiler"}, {"name": "<PERSON>", "githubUsername": "chigix", "url": "https://github.com/chigix"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/less"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "25732dac12dd2e41ec1c5b100ab618c25c0ad9fdd4952fbb4a2e07c1cb0bbdce", "typeScriptVersion": "4.5"}