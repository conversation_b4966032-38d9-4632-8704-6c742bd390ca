{"name": "@types/serve-static", "version": "1.15.7", "description": "TypeScript definitions for serve-static", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "urossmolnik", "url": "https://github.com/urossmolnik"}, {"name": "<PERSON><PERSON>", "githubUsername": "LinusU", "url": "https://github.com/LinusU"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-static"}, "scripts": {}, "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}, "typesPublisherContentHash": "781872d0ec274d5e3360acd4087e25d9d8440401b99181fe8cd1fc968aa78b14", "typeScriptVersion": "4.7"}