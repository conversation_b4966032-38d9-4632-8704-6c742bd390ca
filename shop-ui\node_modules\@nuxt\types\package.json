{"name": "@nuxt/types", "version": "2.17.4", "description": "Nuxt types", "repository": {"type": "git", "url": "git+https://github.com/nuxt/nuxt.git", "directory": "packages/types"}, "license": "MIT", "main": "index.js", "types": "index.d.ts", "files": ["index.js", "**/*.d.ts"], "dependencies": {"@types/babel__core": "7.20.5", "@types/compression": "1.7.5", "@types/connect": "3.4.38", "@types/etag": "1.8.3", "@types/file-loader": "5.0.4", "@types/html-minifier-terser": "7.0.2", "@types/less": "3.0.6", "@types/node": "^16", "@types/optimize-css-assets-webpack-plugin": "5.0.8", "@types/pug": "2.0.10", "@types/serve-static": "1.15.7", "@types/terser-webpack-plugin": "4.2.1", "@types/webpack": "^4.41.38", "@types/webpack-bundle-analyzer": "3.9.5", "@types/webpack-hot-middleware": "2.25.5"}, "engines": {"node": "^14.18.0 || >=16.10.0"}, "publishConfig": {"access": "public"}}